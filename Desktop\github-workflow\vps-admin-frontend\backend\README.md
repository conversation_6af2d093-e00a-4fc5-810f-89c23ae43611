# VPS Admin AI Backend Documentation

This document provides a comprehensive overview of the VPS Admin AI backend project, detailing its architecture, configuration, API, and core components.

## 1. Project Overview

The VPS Admin AI is a sophisticated backend system designed to act as an AI-powered Linux system administrator. It accepts natural language requests from a user, interprets their intent, and translates those requests into a series of executable shell commands to be run on a remote Virtual Private Server (VPS).

The system features two primary operational modes:
1.  A **legacy reactive mode**, where a single AI model determines the next command based on the conversation history and the outcome of the last command.
2.  An advanced **Orchestrator mode**, which employs a multi-agent system to plan, execute, and self-correct complex tasks. This mode breaks down a user's request into a structured plan, executes each step, analyzes failures, and attempts to recover from errors automatically.

The backend is built with FastAPI, communicates with clients via Server-Sent Events (SSE) for real-time updates, and uses Google's Gemini family of models for its AI capabilities.

## 2. Architecture

### Core Components

The project is modular, with each component having a distinct responsibility:

-   **FastAPI App (`main.py`):** The main web server that exposes API endpoints for starting tasks, sending messages, and checking status.
-   **Task Manager (`task_manager.py`):** An in-memory database that creates, stores, and manages the state of each user task. It tracks progress, history, and orchestrator-specific data.
-   **Stream Processor (`stream_processor.py`):** The central hub for handling incoming user messages. It routes requests to either the legacy system or the new Orchestrator based on configuration.
-   **Task Orchestrator (`orchestrator.py`):** The "brain" of the advanced mode. It manages the entire lifecycle of a task, from planning to execution and completion, coordinating between specialized AI agents.
-   **AI Agents (`agents.py`):** A suite of specialized AI models, each designed for a specific sub-task (e.g., planning, refining failed commands, summarizing results).
-   **AI Client (`ai_client.py`):** A client for interacting with the Google Gemini & Gemma APIs, handling prompt construction, intent detection, and response generation.
-   **SSH Client (`ssh_client.py`):** A Paramiko-based client responsible for securely connecting to the remote VPS and executing commands.
-   **System Scanner (`system_scanner.py`):** A utility that performs an initial scan of the remote VPS to gather context like OS version, running services, and installed packages.

### Execution Flow (Orchestrator Mode)

1.  **Task Initiation:** A user sends an initial request (e.g., "Install Docker and deploy my app from GitHub"). The `/start_task` endpoint creates a new task in the `TaskManager`.
2.  **System Scan:** The `SystemScanner` connects to the VPS to gather initial system information.
3.  **Planning Phase:** The `TaskOrchestrator` receives the user's request and invokes the `PlannerAgent`. The agent analyzes the request and breaks it down into a structured, multi-step JSON plan (e.g., update packages, install dependencies, clone repo, run Docker).
4.  **Execution Phase:** The `TaskOrchestrator` begins executing the plan step-by-step.
5.  **Command Confirmation:** For each step, a command is generated. The system sends this command to the user for confirmation (`yes`/`no`).
6.  **Execution & Self-Correction:**
    -   If the user confirms, the `SSHClient` executes the command.
    -   **On Success:** The `SummarizerAgent` creates a brief summary of what was accomplished, which is added to the context history. The orchestrator moves to the next step.
    -   **On Failure:** The `RefinerAgent` is invoked. It analyzes the error message, stdout, and stderr to propose a corrected command or an alternative approach. The orchestrator then asks the user for confirmation on this new command. This loop continues for a configured number of retries.
7.  **Task Completion:** Once all steps are successfully completed, the orchestrator marks the task as `COMPLETED`. If a step fails repeatedly and cannot be corrected, the task is marked as `FAILED`.

## 3. Setup and Configuration

### 3.1. File Structure

```
backend/
├── .env                  # Environment variables for configuration
├── agents.py             # Specialized AI agents (Planner, Executor, etc.)
├── ai_client.py          # Client for interacting with Gemini/Gemma AI
├── config.py             # Configuration loader
├── main.py               # FastAPI application entry point
├── models.py             # Pydantic data models
├── orchestrator.py       # Core task orchestrator engine
├── ORCHESTRATOR_README.md# README for the orchestrator system
├── requirements.txt      # Python dependencies
├── ssh_client.py         # SSH client for remote command execution
├── stream_processor.py   # SSE stream processing logic
└── system_scanner.py     # Initial remote system scanner
```

### 3.2. Dependencies

The project's dependencies are listed in `requirements.txt`:

```
fastapi
uvicorn[standard]>=0.24.0
sse-starlette
pydantic
python-dotenv
google-generativeai
paramiko
google-genai
```

Install them using: `pip install -r requirements.txt`

### 3.3. Environment Variables (`.env`)

The application is configured via a `.env` file.

| Variable                  | Description                                                                                             | Example                                         |
| ------------------------- | ------------------------------------------------------------------------------------------------------- | ----------------------------------------------- |
| `GEMINI_API_KEY`          | **Required.** Your Google AI API key.                                                                   | `AIzaSy...`                                     |
| `GEMINI_MODEL`            | The main Gemini model used for general tasks in the legacy system.                                      | `models/gemini-2.5-flash-preview-05-20`         |
| `INTENT_MODEL`            | The model used for intent detection (chat vs. VPS task). Gemma-27b is used here.                        | `models/gemma-3-27b-it`                         |
| `FLASH_MODEL`             | A specific high-speed model (like Flash 2.5) for orchestrator tasks that benefit from "thinking".       | `gemini-2.5-flash-preview-05-20`                |
| `USE_LLM_INTENT`          | `true` to use the `INTENT_MODEL` for intent detection, `false` to use a simpler keyword-based fallback.   | `true`                                          |
| `USE_THINKING`            | `true` to enable the "thinking" feature of the Flash model, which can improve complex reasoning.        | `true`                                          |
| `MIN_THINKING_PERCENTAGE` | The task difficulty threshold (0-100) above which the "thinking" feature is enabled.                      | `50`                                            |
| `VPS_HOSTNAME`            | **Required.** The hostname or IP address of the remote VPS.                                             | `localhost` or `*************`                  |
| `VPS_PORT`                | The SSH port of the remote VPS.                                                                         | `22` or `4646`                                  |
| `VPS_USERNAME`            | **Required.** The username for the SSH connection.                                                      | `root`                                          |
| `VPS_PASSWORD`            | The password for the SSH connection. **Required if `SSH_PRIVATE_KEY_PATH` is not set.**                   | `root`                                          |
| `SSH_PRIVATE_KEY_PATH`    | The local path to your SSH private key file. **Required if `VPS_PASSWORD` is not set.**                   | `/home/<USER>/.ssh/id_rsa`                        |

## 4. API Endpoints

All endpoints are defined in `main.py`.

---

#### `POST /start_task`

Initiates a new task session. It performs an initial system scan and sets up the AI chat session.

-   **Request Body:** `StartRequest`
    ```json
    {
      "initial_prompt": "Install nginx on my server.",
      "task_metadata": {
        "title": "Nginx Installation Task",
        "priority": "high"
      }
    }
    ```
-   **Response:**
    ```json
    {
      "task_id": "a1b2c3d4-e5f6-..."
    }
    ```

---

#### `POST /send_message`

Sends a message to an existing task and receives a real-time stream of events (SSE). This is the primary endpoint for user interaction.

-   **Request Body:** `MessageRequest`
    ```json
    {
      "message": "yes",
      "task_id": "a1b2c3d4-e5f6-..."
    }
    ```
-   **Response:** An `EventSourceResponse` stream containing JSON-encoded events.

---

#### `GET /task/{task_id}`

Retrieves the current status, history, and metadata for a specific task.

-   **Response Body:** `TaskStatusResponse` (a detailed JSON object).

---

#### `DELETE /task/{task_id}`

Deletes a specific task from the `TaskManager`.

-   **Response:**
    ```json
    {
      "task_id": "a1b2c3d4-e5f6-...",
      "message": "Task deleted successfully"
    }
    ```

---

#### `GET /`

A basic health check endpoint.

---

#### Debug & Recovery Endpoints

-   `POST /task/{task_id}/reset`: Resets a task that is stuck in a processing state.
-   `POST /tasks/recover-stuck`: Automatically finds and resets all stuck tasks.
-   `GET /tasks/debug`: Returns a debug summary of all active tasks.
-   `POST /task/{task_id}/force-status/{new_status}`: Manually sets the status of a task.
-   `POST /task/{task_id}/orchestrator/{mode}`: Enables or disables orchestrator mode (`enable`/`disable`) for a specific task.

## 5. Core Components Documentation

### 5.1. `main.py` - FastAPI Application

This file sets up the FastAPI application, configures CORS middleware, initializes all core components (`TaskManager`, `StreamProcessor`), and defines the API endpoints listed above. It serves as the entry point for the entire backend service.

### 5.2. `config.py` - Configuration Management

-   **`Config` class:** This class loads environment variables from the `.env` file using `python-dotenv`. It performs validation on startup to ensure that critical variables (like `GEMINI_API_KEY` and VPS connection details) are present, exiting gracefully if they are not. It provides a centralized, type-safe way to access configuration throughout the application.

### 5.3. `task_manager.py` - Task Lifecycle Management

-   **`TaskManager` class:** This class acts as an in-memory repository for all tasks.
    -   `tasks: Dict[str, TaskEntry]`: A dictionary holding all task objects, keyed by their UUID.
    -   `create_task()`: Creates a new `TaskEntry` object with a unique ID, initializes its state, and stores it.
    -   `initialize_task()`: A critical asynchronous method that orchestrates the initial system scan and AI chat session setup for a new task.
    -   `get_task()`, `update_task_status()`, etc.: A suite of methods for CRUD operations and state management of tasks.
    -   **Orchestrator Support:** Includes methods specifically for the orchestrator, such as `should_use_orchestrator()`, `update_task_plan()`, and managing context history (`main_context_history`).
    -   **Recovery Methods:** Provides functions like `reset_stuck_task()` and `get_stuck_tasks()` for debugging and recovering tasks that may have frozen due to an unexpected error.

### 5.4. `ai_client.py` - AI Interaction Client

-   **`AIClient` class:** This is the primary interface to the Google AI models.
    -   `_initialize_gemini()`: Configures the Gemini API key and initializes the generative models.
    -   `_detect_user_intent_with_percentage()`: A key method that uses a specified `INTENT_MODEL` (Gemma) to classify user input as either a "chat" request or a "vps" (admin) task. It also attempts to extract a "difficulty percentage" for the task, which can be used to enable advanced features like "thinking".
    -   `_fallback_intent_detection()`: A keyword-based intent detection method used if the LLM-based detection is disabled or fails.
    -   `build_ai_prompt()`: Constructs the complex, context-rich prompt sent to the AI. It includes system information, conversation history, the outcome of the last command, and a detailed set of instructions and security guidelines.
    -   `generate_response_with_thinking()`: An advanced method that leverages the Flash model's "thinking" budget for more complex tasks, potentially improving the quality of the generated commands or plans.
    -   `generate_response()`: The standard method for generating a response from the AI model.
    -   `update_chat_history()`: Manually updates the Gemini chat history to maintain conversation context.

### 5.5. `ssh_client.py` - Secure Shell (SSH) Client

-   **`SSHClient` class:** A wrapper around the `paramiko` library for remote command execution.
    -   `execute_command_async()`: The core method that connects to the remote VPS and executes a given command. It runs the blocking `paramiko` call in a separate thread to avoid blocking the asyncio event loop.
    -   **Authentication:** It intelligently handles authentication, prioritizing SSH key-based auth if a key path is provided, and falling back to password-based auth.
    -   **Error Handling:** It robustly handles various connection and authentication errors.
    -   **Result Formatting:** It returns a structured `SSHResult` model containing the command's stdout, stderr, exit status, and success flag.

### 5.6. `system_scanner.py` - Initial System Analysis

-   **`SystemScanner` class:** Responsible for gathering initial intelligence about the remote server.
    -   `perform_initial_scan()`: The main method that runs a sequence of commands on the remote server to determine:
        -   **OS Information:** Uses `cat /etc/os-release` or `uname` to find the OS distribution and version.
        -   **Running Services:** Uses `systemctl` or `service --status-all` to list active services.
        -   **Installed Packages:** Uses `dpkg-query` (for Debian/Ubuntu) or `rpm` (for CentOS/RHEL) to get a list of installed packages.
    -   The results are formatted into a single string that is provided as initial context to the AI.

### 5.7. `stream_processor.py` - SSE Stream Handling

-   **`StreamProcessor` class:** This class contains the main logic for handling a user's message once a task has been started.
    -   `process_stream()`: The main async generator function that receives a `task_id` and `user_message`. It acts as a state machine.
    -   **Routing:** It checks if the task is configured to use the Orchestrator. If so, it delegates control to `_handle_orchestrator_flow`. Otherwise, it proceeds with the legacy state machine.
    -   `_handle_confirmation_state()`: (Legacy) Handles the user's `yes`/`no` response to a command confirmation.
    -   `_handle_input_state()`: (Legacy) Processes a new user message by calling the AI for the next action.
    -   `_execute_command()`: (Legacy) A helper that calls the `SSHClient` and then passes the result back to the AI for the next step.
    -   `_ask_ai_and_process()`: (Legacy) The core loop of the legacy system: build prompt, get AI response, process response (is it a command, question, or `TASK_COMPLETE`?).

### 5.8. `orchestrator.py` - Task Orchestration Engine

-   **`TaskOrchestrator` class:** The engine for the advanced, multi-agent workflow.
    -   `execute_task()`: The main entry point. It manages the two main phases: planning and execution.
    -   `_planning_phase()`: Calls the `PlannerAgent` to generate a structured `TaskPlan` from the user's initial prompt.
    -   `_execution_phase()`: Iterates through each `TaskStep` in the plan.
    -   `_execute_single_step()`: Manages the execution of a single step, including retries. It determines which command to run (planned, from Executor, or from Refiner) and sends it to the user for confirmation.
    -   `_execute_confirmed_command()`: Executes the command via SSH after user confirmation. It then invokes the `SummarizerAgent` on success or triggers the refinement/recovery logic on failure.
    -   `handle_user_confirmation()`: Processes the user's `yes`/`no` response and continues or aborts the execution flow.
    -   `_should_trigger_error_recovery()`: A simple but effective check to see if a command failure warrants a recovery attempt (currently, any non-zero exit code).

### 5.9. `agents.py` - Specialized AI Agents

This file defines the abstract `BaseAgent` and the concrete agent implementations. Each agent has a highly specific prompt engineered for its task.

-   **`BaseAgent` (ABC):** An abstract base class that handles model initialization and provides a common `_generate_response` method.
-   **`PlannerAgent`:** Takes the user's request and system info and produces a JSON array of steps, where each step has a `description` and a `command`.
-   **`ExecutorAgent`:** (Used in legacy mode or if a planned step lacks a command). It takes a single step description and generates a single, non-interactive shell command.
-   **`RefinerAgent`:** This is the core of the self-correction mechanism. It is given the original objective and a history of failed command attempts (with their error output) and is tasked with generating a corrected command or a completely new approach.
-   **`SummarizerAgent`:** After a command succeeds, this agent takes the command, its output, and the original step description to create a concise, human-readable summary in the past tense (e.g., "Successfully installed Nginx web server.").
-   **`ErrorRecoveryPlanner`:** A specialized agent designed to create a multi-step *recovery plan* when a command fails fundamentally. It analyzes the error and proposes a new sequence of commands to fix the underlying issue before retrying the original step.

### 5.10. `models.py` - Data Models

This file contains all Pydantic models used for API requests/responses and for internal state management.

-   **Request/Response Models:**
    -   `StartRequest`, `MessageRequest`: Define the structure of incoming API requests.
    -   `TaskStatusResponse`: Defines the rich structure of the `/task/{task_id}` endpoint response, including orchestrator-specific fields.
    -   `SSHResult`: A structured model for the outcome of an SSH command.
-   **Orchestrator Models:**
    -   `TaskStatus`, `StepStatus`: Enums for tracking the state of tasks and steps.
    -   `TaskStep`: Represents a single step in a plan, with its description, command, status, and execution attempts.
    -   `TaskPlan`: Contains the full list of `TaskStep` objects for a task.
    -   `StepAttempt`: Records the details of a single attempt to execute a step's command, including the `SSHResult`.
-   **Agent Response Models:**
    -   `AgentResponse` (Base), `PlannerResponse`, `ExecutorResponse`, `RefinerResponse`, `SummarizerResponse`, `ErrorRecoveryResponse`: Structured models for the output of each AI agent, ensuring type safety and consistency.
-   **Core Internal Model:**
    -   `TaskEntry`: The most important internal model. It represents a single task in the `TaskManager` and holds all its state, including the Gemini chat object, status, system info, command history, and all the new fields required for the orchestrator (`task_plan`, `main_context_history`, `current_step_attempts`, etc.).

## 6. Included README (`ORCHESTRATOR_README.md`)

This section contains the original, verbatim content from `ORCHESTRATOR_README.md` for reference.

---

# VPS Admin Orchestrator System

## Overview

The VPS Admin system has been enhanced with a sophisticated multi-component orchestrator that replaces the single reactive agent with a more intelligent, step-by-step task execution system.

## Architecture

### Core Components

1.  **TaskOrchestrator** - The central "brain" that manages task execution
2.  **PlannerAgent** - Breaks down user requests into executable steps
3.  **ExecutorAgent** - Converts steps into specific shell commands
4.  **RefinerAgent** - Analyzes failures and suggests corrections
5.  **SummarizerAgent** - Creates concise summaries of completed steps

### Execution Flow

1.  **Planning Phase**: User request is analyzed and broken into structured steps
2.  **Execution Phase**: Each step is executed with automatic retry and refinement
3.  **Self-Correction**: Failed commands are analyzed and alternative approaches are tried
4.  **Progress Tracking**: Detailed progress updates and context management

## Key Features

### Intelligent Task Decomposition

-   Automatically breaks complex tasks into logical steps
-   Considers dependencies and prerequisites
-   Provides clear progress tracking

### Self-Correcting Execution

-   Analyzes command failures and suggests fixes
-   Tries alternative approaches when commands fail
-   Learns from previous attempts within the same step

### Enhanced Context Management

-   Maintains main context history of successful steps
-   Scoped memory for current step attempts
-   Rich progress tracking and metadata

### Security-Aware

-   Analyzes commands for security risks
-   Categorizes command types (package management, service management, etc.)
-   Provides security warnings for potentially dangerous operations

## Configuration

### Environment Variables

```bash
# Enable/disable orchestrator system
USE_ORCHESTRATOR=true

# Maximum retries per step
MAX_RETRIES_PER_STEP=3

# Orchestrator timeout (seconds)
ORCHESTRATOR_TIMEOUT=300
```

### Task-Level Control

You can enable/disable orchestrator mode per task:

```bash
# Enable orchestrator for a specific task
POST /task/{task_id}/orchestrator/enable

# Disable orchestrator for a specific task
POST /task/{task_id}/orchestrator/disable
```

## API Enhancements

### Enhanced Task Status Response

The task status endpoint now includes orchestrator-specific fields:

```json
{
  "task_id": "uuid",
  "status": "EXECUTING",
  "use_orchestrator": true,
  "task_plan": {
    "steps": [
      {
        "step_number": 1,
        "description": "Update package lists",
        "status": "COMPLETED",
        "command": "sudo apt update",
        "summary": "Successfully updated package lists",
        "attempts": 1
      }
    ],
    "total_steps": 5,
    "current_step": 1
  },
  "main_context_history": [
    "Step 1: Successfully updated package lists"
  ],
  "current_step_attempts": 0,
  "final_output": ""
}
```

### New Event Types

The orchestrator system introduces new SSE event types:

-   `plan_created` - When a task plan is generated
-   `step_start` - When a step begins execution
-   `step_complete` - When a step completes successfully
-   `task_failed` - When a task fails after all retries
-   `task_complete` - When all steps complete successfully

## Backward Compatibility

The system maintains full backward compatibility:

-   Legacy tasks continue to work with the original system
-   New tasks use the orchestrator by default (configurable)
-   All existing API endpoints remain unchanged
-   Frontend integration requires no changes

## Benefits

### For Users

-   **Clearer Progress**: Step-by-step progress with detailed feedback
-   **Better Reliability**: Automatic retry and error correction
-   **Enhanced Security**: Built-in security analysis and warnings
-   **Improved Transparency**: Clear understanding of what's happening

### For Developers

-   **Modular Design**: Specialized agents for different tasks
-   **Extensible**: Easy to add new agent types or capabilities
-   **Testable**: Individual components can be tested in isolation
-   **Maintainable**: Clear separation of concerns

## Example Usage

### Simple Task

```javascript
User: "Install Docker"

Orchestrator Flow:
1. Planning: Breaks into steps (update packages, install prerequisites, add GPG key, etc.)
2. Execution: Executes each step with automatic retry
3. Self-Correction: If a step fails, analyzes error and tries alternative approach
4. Completion: Provides summary of what was accomplished
```

### Complex Task with Failures

```javascript
User: "Set up a web server with SSL"

Orchestrator Flow:
1. Planning: Creates 8-step plan
2. Step 3 fails: "Install Nginx" fails due to missing repository
3. Refiner analyzes: Suggests adding repository first
4. Retry succeeds: Nginx installed successfully
5. Continues with remaining steps
6. Completion: Full web server with SSL configured
```

## Monitoring and Debugging

### Logs

-   Detailed logging for each orchestrator component
-   Step-by-step execution tracking
-   Error analysis and retry attempts

### Status Tracking

-   Real-time progress updates
-   Detailed step status and attempt history
-   Context preservation across steps

## Future Enhancements

-   **Learning System**: Learn from successful patterns across tasks
-   **Parallel Execution**: Execute independent steps in parallel
-   **Rollback Capability**: Automatic rollback on critical failures
-   **Custom Agents**: Plugin system for domain-specific agents