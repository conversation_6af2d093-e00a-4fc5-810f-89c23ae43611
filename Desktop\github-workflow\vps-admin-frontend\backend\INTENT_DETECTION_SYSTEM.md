# Intent Detection System Documentation

## Overview

The VPS Admin system uses a sophisticated intent detection mechanism powered by Gemma-27b to intelligently route user messages between chat responses and VPS administration tasks. This system minimizes API calls while providing optimal model selection based on task complexity.

## System Architecture

### 1. Intent Detection Flow

```
User Message → Gemma-27b Intent Detection → Route Decision
                                         ↓
                    ┌─────────────────────┼─────────────────────┐
                    ↓                     ↓                     ↓
               #Chat Response      #VPS:<50%           #VPS:≥50%
                    ↓                     ↓                     ↓
            Direct Response      Regular Gemini      Flash 2.5 + Thinking
            (No additional       (No thinking)       (With thinking budget)
             AI calls)
```

### 2. Models Used

- **Intent Detection**: `models/gemma-3-27b-it` (Gemma-27b)
- **Regular VPS Tasks**: `models/gemini-2.5-flash-preview-05-20` (Flash 2.5)
- **Complex VPS Tasks**: `gemini-2.5-flash-preview-05-20` with thinking budget
- **Chat Responses**: Direct from Gemma-27b (no additional model calls)

## Intent Detection Prompt

The system uses this exact prompt format for Gemma-27b:

```
you are a vps admin assistant, if a user has a chatting intent you can respond with:
#Chat:(chatting_response or msg)
if the user has an intent of administering his vps or doing a system task or running a terminal command(s) you can respond with:
#VPS:(0%-100%) (the 0%-100% is the the percentage of how hard the task given)
note: don't respond with anything after these tags this will count

user: {user_message}
```

## Response Parsing

### Chat Intent
- **Format**: `#Chat:Hello! How can I help you today?`
- **Action**: Return the chat response directly to user
- **API Calls**: 1 (only Gemma-27b for intent detection)

### VPS Intent
- **Format**: `#VPS:75%` or `#VPS:30%`
- **Action**: Extract percentage and route to appropriate model
- **API Calls**: 2 (Gemma-27b + chosen model)

## Thinking Budget System

### Configuration
- **Minimum Thinking Percentage**: 50% (configurable via `MIN_THINKING_PERCENTAGE`)
- **Maximum Thinking Budget**: 24,576 tokens
- **Formula**: `(percentage - 50) / 50 * 24576`

### Examples
- **30% difficulty**: No thinking (0 budget)
- **50% difficulty**: No thinking (0 budget) 
- **75% difficulty**: 12,288 thinking budget
- **100% difficulty**: 24,576 thinking budget (maximum)

## Implementation Details

### Key Files
- `backend/ai_client.py` - Intent detection and model routing
- `backend/config.py` - Configuration and environment variables
- `backend/stream_processor.py` - Response handling and streaming
- `backend/.env` - Model and API configuration

### Environment Variables
```env
# Intent Detection
INTENT_MODEL=models/gemma-3-27b-it
USE_LLM_INTENT=true

# Flash 2.5 Configuration
FLASH_MODEL=gemini-2.5-flash-preview-05-20
USE_THINKING=true
MIN_THINKING_PERCENTAGE=50

# Main Model
GEMINI_MODEL=models/gemini-2.5-flash-preview-05-20
```

### Caching Mechanism
- Intent detection results are cached by message content
- Cache key: `user_message.lower().strip()`
- Prevents redundant API calls for repeated messages

## API Call Optimization

### Scenarios and Call Counts

1. **Chat Intent (Cached)**
   - API Calls: 0
   - Response: Immediate from cache

2. **Chat Intent (New)**
   - API Calls: 1 (Gemma-27b only)
   - Response: Direct chat response

3. **Simple VPS Task (<50%)**
   - API Calls: 2 (Gemma-27b + Regular Flash 2.5)
   - Response: Command or text response

4. **Complex VPS Task (≥50%)**
   - API Calls: 2 (Gemma-27b + Flash 2.5 with thinking)
   - Response: Command or text response with enhanced reasoning

## Error Handling

### Fallback Mechanisms
1. **Gemma-27b Failure**: Falls back to keyword-based detection
2. **Flash 2.5 Failure**: Falls back to regular Gemini model
3. **Invalid Response Format**: Uses default percentage (70%)

### Keyword Fallback
When LLM intent detection fails, the system uses keyword matching:
- **Admin keywords**: install, configure, setup, start, stop, restart, etc.
- **Chat keywords**: hello, hi, thanks, help, how are you, etc.

## Performance Characteristics

### Response Times
- **Cached Intent**: ~0ms
- **Chat Response**: ~1-2 seconds (single API call)
- **Simple VPS**: ~2-4 seconds (two API calls)
- **Complex VPS**: ~3-6 seconds (two API calls + thinking)

### Token Usage
- **Intent Detection**: ~50-100 tokens per call
- **Chat Response**: Included in intent detection
- **VPS Tasks**: Variable based on context and complexity

## Configuration Options

### Disabling Features
```env
# Disable LLM intent detection (use keyword fallback only)
USE_LLM_INTENT=false

# Disable thinking for all tasks
USE_THINKING=false

# Adjust thinking threshold
MIN_THINKING_PERCENTAGE=60
```

### Model Customization
```env
# Use different model for intent detection
INTENT_MODEL=models/gemma-2-9b-it

# Use different model for VPS tasks
FLASH_MODEL=gemini-1.5-pro
```

## Monitoring and Debugging

### Debug Logs
The system provides detailed logging for:
- Intent detection results
- Model selection decisions
- Thinking budget calculations
- API call timings
- Cache hit/miss ratios

### Log Examples
```
DEBUG: Gemma-27b raw response: '#VPS:75%'
DEBUG: Detected VPS intent with 75% difficulty
DEBUG: Percentage 75% -> Thinking budget: 12288
DEBUG: Using Flash 2.5 with thinking budget 12288 for 75% difficulty task
```

## Future Enhancements

### Potential Improvements
1. **Dynamic Model Selection**: Choose models based on task type
2. **Adaptive Thresholds**: Adjust thinking percentage based on success rates
3. **Multi-language Support**: Extend intent detection to other languages
4. **Custom Prompts**: Allow per-user or per-task prompt customization
5. **Analytics Dashboard**: Track intent detection accuracy and performance

### Experimental Features
1. **Intent Confidence Scoring**: Add confidence levels to intent detection
2. **Context-Aware Detection**: Use conversation history for better intent detection
3. **Task Complexity Prediction**: ML model to predict task difficulty
4. **Hybrid Routing**: Combine multiple models for optimal results
